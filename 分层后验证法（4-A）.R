# 分层后验证法-合并版
# 2-5分层后验证法脚本合并，涵盖事件分布、时间分布、协变量、特殊结构等多维度验证
#
# 输出说明：
# - 所有可视化图表和报告将保存到: D:/FG/分层后验证
# - 包含7个PNG图表文件和1个TXT总结报告
# - 脚本会自动创建输出目录（如果不存在）

# ========== 0. 加载必要的包 ==========
library(readxl)      # 读取Excel文件
library(dplyr)       # 数据处理
library(ggplot2)     # 数据可视化
library(gridExtra)   # 合并图表展示
library(tidyr)       # 数据整形
library(reshape2)    # 数据重塑
library(car)         # Levene方差同质性检验
library(tableone)    # 协变量平衡性评估
library(rstatix)     # 统计分析工具
library(gtsummary)   # 美观统计表格
library(survival)    # 生存分析
library(cmprsk)      # 竞争风险分析
library(survminer)   # 生存曲线美化

# ========== 1. 读取数据和设置输出路径 ==========
original_path <- "D:/FG/EXCEL/R-clean (2).xlsx"
train_path <- "D:/FG/EXCEL/R-train.xlsx"
valid_path <- "D:/FG/EXCEL/R-valid.xlsx"

# 设置输出目录
output_dir <- "D:/FG/分层后验证"
if (!dir.exists(output_dir)) {
  dir.create(output_dir, recursive = TRUE)
  cat("创建输出目录:", output_dir, "\n")
} else {
  cat("输出目录已存在:", output_dir, "\n")
}

# 清理输出目录中的旧文件（可选）
# 如果需要清理旧文件，请取消下面几行的注释
# old_files <- list.files(output_dir, pattern = "\\.(png|txt)$", full.names = TRUE)
# if (length(old_files) > 0) {
#   file.remove(old_files)
#   cat("已清理", length(old_files), "个旧文件\n")
# }

original_data <- read_excel(original_path)
train_data <- read_excel(train_path)
valid_data <- read_excel(valid_path)

n_cols <- ncol(original_data)
time_col_idx <- n_cols - 1
vital_col_idx <- n_cols
id_col_idx <- 1

original_time <- original_data[[time_col_idx]]
train_time <- train_data[[time_col_idx]]
valid_time <- valid_data[[time_col_idx]]

original_vital <- original_data[[vital_col_idx]]
train_vital <- train_data[[vital_col_idx]]
valid_vital <- valid_data[[vital_col_idx]]

event_labels <- c("0" = "删失", "1" = "主要事件", "2" = "竞争事件")

# ========== 2. 事件分布验证 ==========
cat("\n【一、事件分布验证】\n")
calc_distribution <- function(vital, dataset_name) {
  freq_table <- table(vital)
  pct_table <- prop.table(freq_table) * 100
  result <- data.frame(
    数据集 = dataset_name,
    事件类型 = names(freq_table),
    频数 = as.numeric(freq_table),
    百分比 = round(as.numeric(pct_table), 2)
  )
  result$事件类型标签 <- event_labels[result$事件类型]
  return(result)
}
original_dist <- calc_distribution(original_vital, "原始数据")
train_dist <- calc_distribution(train_vital, "训练集")
valid_dist <- calc_distribution(valid_vital, "验证集")
all_dist <- rbind(original_dist, train_dist, valid_dist)
print("各数据集事件分布情况:")
print(all_dist)
cat("\n训练集与原始数据事件分布卡方检验:\n")
train_chisq <- chisq.test(table(train_vital), p = prop.table(table(original_vital)))
print(train_chisq)
cat("\n验证集与原始数据事件分布卡方检验:\n")
valid_chisq <- chisq.test(table(valid_vital), p = prop.table(table(original_vital)))
print(valid_chisq)
plot_data <- all_dist %>% mutate(事件类型标签 = factor(事件类型标签, levels = c("删失", "主要事件", "竞争事件")))
# 事件分布条形图 - 与图片样式一致
p1 <- ggplot(plot_data, aes(x = 数据集, y = 频数, fill = 事件类型标签)) +
  geom_bar(stat = "identity", position = "dodge", alpha = 0.8, width = 0.7) +
  theme_minimal() +
  labs(title = "各数据集事件频数分布", x = "", y = "频数") +
  scale_fill_manual(values = c("删失" = "#7FC97F",
                              "主要事件" = "#FDC086",
                              "竞争事件" = "#BEAED4"),
                   name = "事件类型") +
  theme(
    plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
    axis.text.x = element_text(angle = 45, hjust = 1, size = 10),
    axis.text.y = element_text(size = 10),
    axis.title.y = element_text(size = 12),
    legend.title = element_text(size = 12),
    legend.text = element_text(size = 10),
    panel.grid.major = element_line(color = "grey90", size = 0.5),
    panel.grid.minor = element_line(color = "grey95", size = 0.3),
    panel.background = element_rect(fill = "white", color = NA)
  )

p2 <- ggplot(plot_data, aes(x = 数据集, y = 百分比, fill = 事件类型标签)) +
  geom_bar(stat = "identity", position = "dodge", alpha = 0.8, width = 0.7) +
  theme_minimal() +
  labs(title = "各数据集事件百分比分布", x = "", y = "百分比 (%)") +
  scale_fill_manual(values = c("删失" = "#7FC97F",
                              "主要事件" = "#FDC086",
                              "竞争事件" = "#BEAED4"),
                   name = "事件类型") +
  theme(
    plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
    axis.text.x = element_text(angle = 45, hjust = 1, size = 10),
    axis.text.y = element_text(size = 10),
    axis.title.y = element_text(size = 12),
    legend.title = element_text(size = 12),
    legend.text = element_text(size = 10),
    panel.grid.major = element_line(color = "grey90", size = 0.5),
    panel.grid.minor = element_line(color = "grey95", size = 0.3),
    panel.background = element_rect(fill = "white", color = NA)
  )

# 保存事件分布图
event_dist_plot <- grid.arrange(p1, p2, ncol = 1)
ggsave(file.path(output_dir, "01_事件分布图.png"),
       event_dist_plot, width = 12, height = 10, dpi = 300)
cat("事件分布图已保存到:", file.path(output_dir, "01_事件分布图.png"), "\n")
sample_counts <- data.frame(
  数据集 = c("原始数据", "训练集", "验证集"),
  样本量 = c(length(original_vital), length(train_vital), length(valid_vital))
)
sample_counts$比例 <- round(sample_counts$样本量 / sample_counts$样本量[1] * 100, 2)
cat("\n各数据集样本量情况:\n")
print(sample_counts)
cat("\n数据分层拆分评估结论:\n")
p_value_threshold <- 0.05
if (train_chisq$p.value > p_value_threshold && valid_chisq$p.value > p_value_threshold) {
  cat("训练集和验证集的事件分布与原始数据无显著差异 (p值均 > 0.05)，\n")
  cat("说明分层抽样成功保持了原始数据中各类事件的分布比例。\n")
} else {
  cat("注意：某些数据集的事件分布与原始数据存在统计学差异，\n")
  cat("可能需要重新调整分层抽样策略。\n")
}
cat("\n各类事件的训练集vs验证集比例:\n")
event_split <- data.frame(
  事件类型 = c("删失", "主要事件", "竞争事件"),
  训练集数量 = as.numeric(table(train_vital)),
  验证集数量 = as.numeric(table(valid_vital))
)
event_split$总数 <- event_split$训练集数量 + event_split$验证集数量
event_split$训练集比例 <- round(event_split$训练集数量 / event_split$总数 * 100, 2)
event_split$验证集比例 <- round(event_split$验证集数量 / event_split$总数 * 100, 2)
print(event_split)

# ========== 3. 时间分布验证 ==========
cat("\n【二、时间分布验证】\n")
get_time_stats <- function(time_data) {
  return(c(
    mean = mean(time_data, na.rm = TRUE),
    median = median(time_data, na.rm = TRUE),
    sd = sd(time_data, na.rm = TRUE),
    min = min(time_data, na.rm = TRUE),
    max = max(time_data, na.rm = TRUE),
    q25 = quantile(time_data, 0.25, na.rm = TRUE),
    q75 = quantile(time_data, 0.75, na.rm = TRUE)
  ))
}
original_stats <- get_time_stats(original_time)
train_stats <- get_time_stats(train_time)
valid_stats <- get_time_stats(valid_time)
time_stats_df <- data.frame(
  统计量 = c("均值", "中位数", "标准差", "最小值", "最大值", "25%分位数", "75%分位数"),
  原始数据 = round(original_stats, 2),
  训练集 = round(train_stats, 2),
  验证集 = round(valid_stats, 2)
)
cat("时间变量基本描述性统计:\n")
print(time_stats_df)
cat("\n训练集与原始数据时间分布的KS检验:\n")
ks_train <- ks.test(train_time, original_time)
print(ks_train)
cat("\n验证集与原始数据时间分布的KS检验:\n")
ks_valid <- ks.test(valid_time, original_time)
print(ks_valid)
original_df <- data.frame(时间 = original_time, 数据集 = "原始数据")
train_df <- data.frame(时间 = train_time, 数据集 = "训练集")
valid_df <- data.frame(时间 = valid_time, 数据集 = "验证集")
time_df <- rbind(original_df, train_df, valid_df)
time_df$数据集 <- factor(time_df$数据集, levels = c("原始数据", "训练集", "验证集"))
# 时间分布箱线图和密度图 - 与图片样式一致
p1 <- ggplot(time_df, aes(x = 数据集, y = 时间, fill = 数据集)) +
  geom_boxplot(alpha = 0.8, outlier.size = 1) +
  theme_minimal() +
  labs(title = "各数据集时间分布箱线图", x = "", y = "时间（天）") +
  scale_fill_manual(values = c("原始数据" = "#7FC97F",
                              "训练集" = "#FDC086",
                              "验证集" = "#BEAED4"),
                   name = "数据集") +
  theme(
    plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
    axis.text.x = element_text(angle = 45, hjust = 1, size = 10),
    axis.text.y = element_text(size = 10),
    axis.title.y = element_text(size = 12),
    legend.title = element_text(size = 12),
    legend.text = element_text(size = 10),
    panel.grid.major = element_line(color = "grey90", size = 0.5),
    panel.grid.minor = element_line(color = "grey95", size = 0.3),
    panel.background = element_rect(fill = "white", color = NA)
  )

p2 <- ggplot(time_df, aes(x = 时间, fill = 数据集)) +
  geom_density(alpha = 0.6) +
  theme_minimal() +
  labs(title = "各数据集时间分布密度图", x = "时间（天）", y = "密度") +
  scale_fill_manual(values = c("原始数据" = "#7FC97F",
                              "训练集" = "#FDC086",
                              "验证集" = "#BEAED4"),
                   name = "数据集") +
  theme(
    plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
    axis.text.x = element_text(size = 10),
    axis.text.y = element_text(size = 10),
    axis.title.x = element_text(size = 12),
    axis.title.y = element_text(size = 12),
    legend.title = element_text(size = 12),
    legend.text = element_text(size = 10),
    panel.grid.major = element_line(color = "grey90", size = 0.5),
    panel.grid.minor = element_line(color = "grey95", size = 0.3),
    panel.background = element_rect(fill = "white", color = NA)
  )

# 保存时间分布图
time_dist_plot <- grid.arrange(p1, p2, ncol = 1)
ggsave(file.path(output_dir, "02_时间分布图.png"),
       time_dist_plot, width = 12, height = 10, dpi = 300)
cat("时间分布图已保存到:", file.path(output_dir, "02_时间分布图.png"), "\n")
original_event_df <- data.frame(
  时间 = original_time,
  事件类型 = factor(original_vital, labels = c("删失", "主要事件", "竞争事件")),
  数据集 = "原始数据"
)
train_event_df <- data.frame(
  时间 = train_time,
  事件类型 = factor(train_vital, labels = c("删失", "主要事件", "竞争事件")),
  数据集 = "训练集"
)
valid_event_df <- data.frame(
  时间 = valid_time,
  事件类型 = factor(valid_vital, labels = c("删失", "主要事件", "竞争事件")),
  数据集 = "验证集"
)
event_time_df <- rbind(original_event_df, train_event_df, valid_event_df)
event_stats <- event_time_df %>%
  group_by(数据集, 事件类型) %>%
  summarise(
    样本量 = n(),
    均值 = mean(时间, na.rm = TRUE),
    中位数 = median(时间, na.rm = TRUE),
    标准差 = sd(时间, na.rm = TRUE),
    最小值 = min(时间, na.rm = TRUE),
    最大值 = max(时间, na.rm = TRUE)
  )
cat("\n按事件类型分组的时间描述性统计:\n")
print(event_stats)
# 创建与图片样式一致的箱线图
p3 <- ggplot(event_time_df, aes(x = 数据集, y = 时间, fill = 数据集)) +
  geom_boxplot(alpha = 0.8, outlier.size = 1) +
  facet_wrap(~事件类型, ncol = 3, scales = "free_y") +
  theme_minimal() +
  labs(title = "各事件类型的时间分布箱线图",
       x = "",
       y = "时间（天）") +
  scale_fill_manual(values = c("原始数据" = "#7FC97F",
                              "训练集" = "#FDC086",
                              "验证集" = "#BEAED4"),
                   name = "数据集") +
  theme(
    plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
    axis.text.x = element_text(angle = 45, hjust = 1, size = 10),
    axis.text.y = element_text(size = 10),
    axis.title.y = element_text(size = 12),
    legend.title = element_text(size = 12),
    legend.text = element_text(size = 10),
    strip.text = element_text(size = 12, face = "bold"),
    panel.grid.major = element_line(color = "grey90", size = 0.5),
    panel.grid.minor = element_line(color = "grey95", size = 0.3),
    panel.background = element_rect(fill = "white", color = NA),
    strip.background = element_rect(fill = "grey95", color = "grey80")
  )
# 保存按事件类型分组的时间分布图
ggsave(file.path(output_dir, "03_按事件类型时间分布图.png"),
       p3, width = 15, height = 8, dpi = 300)
cat("按事件类型时间分布图已保存到:", file.path(output_dir, "03_按事件类型时间分布图.png"), "\n")
print(p3)
cat("\n方差分析检验三个数据集的时间分布差异:\n")
aov_result <- aov(时间 ~ 数据集, data = time_df)
print(summary(aov_result))
cat("\nLevene方差齐性检验:\n")
levene_test <- leveneTest(时间 ~ 数据集, data = time_df)
print(levene_test)
cat("\n时间分布评估结论:\n")
if (ks_train$p.value > p_value_threshold && ks_valid$p.value > p_value_threshold) {
  cat("1. Kolmogorov-Smirnov检验表明，训练集和验证集的时间分布与原始数据无显著差异（p值均 > 0.05）。\n")
} else {
  cat("1. 注意：Kolmogorov-Smirnov检验显示，某些数据集的时间分布与原始数据存在统计学差异。\n")
}
if (summary(aov_result)[[1]][["Pr(>F)"]][1] > p_value_threshold) {
  cat("2. 方差分析结果表明，三个数据集的时间平均值无显著差异（p值 > 0.05）。\n")
} else {
  cat("2. 注意：方差分析显示，三个数据集的时间平均值存在显著差异（p值 < 0.05）。\n")
}
if (levene_test$`Pr(>F)`[1] > p_value_threshold) {
  cat("3. Levene检验表明，三个数据集的时间方差无显著差异（p值 > 0.05）。\n")
} else {
  cat("3. 注意：Levene检验显示，三个数据集的时间方差存在显著差异（p值 < 0.05）。\n")
}
all_tests_passed <- ks_train$p.value > p_value_threshold && 
  ks_valid$p.value > p_value_threshold && 
  summary(aov_result)[[1]][["Pr(>F)"]][1] > p_value_threshold &&
  levene_test$`Pr(>F)`[1] > p_value_threshold
if (all_tests_passed) {
  cat("\n总结：各项统计检验均表明，训练集和验证集的时间分布与原始数据保持一致，\n")
  cat("说明按照事件类型进行的分层抽样同时也良好地保持了时间数据的分布特性。\n")
} else {
  cat("\n总结：某些统计检验表明数据集之间存在差异，建议检查时间分布的细节，\n")
  cat("可能需要考虑在分层抽样时同时考虑事件类型和时间特征。\n")
}

# ========== 4. 协变量分布验证 ==========
cat("\n【三、协变量分布验证】\n")
covariate_indices <- 2:(time_col_idx-1)
covariate_names <- names(original_data)[covariate_indices]
is_binary <- function(x) length(unique(na.omit(x))) <= 2
binary_vars <- sapply(original_data[covariate_indices], is_binary)
binary_var_names <- covariate_names[binary_vars]
continuous_var_names <- covariate_names[!binary_vars]
cat("二分类变量:", paste(binary_var_names, collapse=", "), "\n\n")
cat("连续变量:", paste(continuous_var_names, collapse=", "), "\n\n")
train_orig <- rbind(original_data, train_data)
train_orig$group <- c(rep("原始数据", nrow(original_data)), rep("训练集", nrow(train_data)))
valid_orig <- rbind(original_data, valid_data)
valid_orig$group <- c(rep("原始数据", nrow(original_data)), rep("验证集", nrow(valid_data)))
train_table <- CreateTableOne(vars = covariate_names, strata = "group", data = train_orig, factorVars = binary_var_names, test = TRUE, smd = TRUE)
valid_table <- CreateTableOne(vars = covariate_names, strata = "group", data = valid_orig, factorVars = binary_var_names, test = TRUE, smd = TRUE)
train_smd <- ExtractSmd(train_table)
valid_smd <- ExtractSmd(valid_table)
smd_df <- data.frame(
  变量 = rownames(train_smd),
  训练集_vs_原始 = as.numeric(train_smd),
  验证集_vs_原始 = as.numeric(valid_smd)
)
smd_long <- reshape2::melt(smd_df, id.vars = "变量")
names(smd_long) <- c("变量", "比较", "SMD")
# SMD图表 - 与图片样式一致
smd_plot <- ggplot(smd_long, aes(x = 变量, y = SMD, fill = 比较)) +
  geom_col(position = "dodge", alpha = 0.8, width = 0.7) +
  geom_hline(yintercept = 0.1, linetype = "dashed", color = "red", size = 1) +
  coord_flip() +
  theme_minimal() +
  labs(title = "协变量的标准化均值差(SMD)",
       subtitle = "虚线表示0.1的阈值，通常认为低于此值的差异不显著",
       x = "变量",
       y = "SMD") +
  scale_fill_manual(values = c("训练集_vs_原始" = "#FDC086",
                              "验证集_vs_原始" = "#BEAED4"),
                   name = "比较组") +
  theme(
    plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
    plot.subtitle = element_text(hjust = 0.5, size = 11),
    axis.text.x = element_text(size = 10),
    axis.text.y = element_text(size = 10),
    axis.title.x = element_text(size = 12),
    axis.title.y = element_text(size = 12),
    legend.title = element_text(size = 12),
    legend.text = element_text(size = 10),
    panel.grid.major = element_line(color = "grey90", size = 0.5),
    panel.grid.minor = element_line(color = "grey95", size = 0.3),
    panel.background = element_rect(fill = "white", color = NA)
  )
# 保存SMD图
ggsave(file.path(output_dir, "04_协变量SMD图.png"),
       smd_plot, width = 12, height = 8, dpi = 300)
cat("协变量SMD图已保存到:", file.path(output_dir, "04_协变量SMD图.png"), "\n")
print(smd_plot)
cat("\n训练集与原始数据的协变量平衡:\n")
print(train_table, smd = TRUE)
cat("\n验证集与原始数据的协变量平衡:\n")
print(valid_table, smd = TRUE)
if(length(continuous_var_names) > 0) {
  cat("\n\n连续型协变量的详细分析:\n")
  for (var in continuous_var_names) {
    train_stats <- train_orig %>% group_by(group) %>% summarise(均值 = mean(.data[[var]], na.rm = TRUE), 标准差 = sd(.data[[var]], na.rm = TRUE), 中位数 = median(.data[[var]], na.rm = TRUE), IQR = IQR(.data[[var]], na.rm = TRUE))
    t_test_result <- t.test(as.formula(paste(var, "~ group")), data = train_orig)
    cat("\n变量:", var, "（训练集 vs 原始数据）\n")
    print(train_stats)
    cat("t检验 p值:", t_test_result$p.value, "\n")
    p <- ggplot(train_orig, aes(x = group, y = .data[[var]], fill = group)) + geom_boxplot(alpha = 0.7) + theme_minimal() + labs(title = paste("变量:", var, "(训练集 vs 原始数据)"), y = var, x = "") + scale_fill_brewer(palette = "Set2")
    print(p)
    valid_stats <- valid_orig %>% group_by(group) %>% summarise(均值 = mean(.data[[var]], na.rm = TRUE), 标准差 = sd(.data[[var]], na.rm = TRUE), 中位数 = median(.data[[var]], na.rm = TRUE), IQR = IQR(.data[[var]], na.rm = TRUE))
    t_test_result <- t.test(as.formula(paste(var, "~ group")), data = valid_orig)
    cat("\n变量:", var, "（验证集 vs 原始数据）\n")
    print(valid_stats)
    cat("t检验 p值:", t_test_result$p.value, "\n")
    p <- ggplot(valid_orig, aes(x = group, y = .data[[var]], fill = group)) + geom_boxplot(alpha = 0.7) + theme_minimal() + labs(title = paste("变量:", var, "(验证集 vs 原始数据)"), y = var, x = "") + scale_fill_brewer(palette = "Set2")
    print(p)
  }
}
if(length(binary_var_names) > 0) {
  cat("\n\n二分类协变量的详细分析:\n")
  for (var in binary_var_names) {
    train_table <- table(train_orig$group, train_orig[[var]])
    train_prop <- prop.table(train_table, margin = 1) * 100
    expected <- sum(train_table) * outer(rowSums(train_table)/sum(train_table), colSums(train_table)/sum(train_table))
    min_expected <- min(expected)
    if (min_expected < 5) {
      train_test <- fisher.test(train_table)
      test_name <- "Fisher精确检验"
    } else {
      train_test <- chisq.test(train_table)
      test_name <- "卡方检验"
    }
    cat("\n变量:", var, "（训练集 vs 原始数据）\n")
    print(train_table)
    cat("比例 (%):\n")
    print(round(train_prop, 2))
    cat(test_name, "p值:", train_test$p.value, "\n")
    train_prop_df <- as.data.frame(train_prop)
    colnames(train_prop_df) <- c("group", "值", "比例")
    p <- ggplot(train_prop_df, aes(x = group, y = 比例, fill = factor(值))) + geom_bar(stat = "identity", position = "dodge") + theme_minimal() + labs(title = paste("变量:", var, "(训练集 vs 原始数据)"), y = "比例 (%)", x = "", fill = "值") + scale_fill_brewer(palette = "Set3")
    print(p)
    valid_table <- table(valid_orig$group, valid_orig[[var]])
    valid_prop <- prop.table(valid_table, margin = 1) * 100
    expected <- sum(valid_table) * outer(rowSums(valid_table)/sum(valid_table), colSums(valid_table)/sum(valid_table))
    min_expected <- min(expected)
    if (min_expected < 5) {
      valid_test <- fisher.test(valid_table)
      test_name <- "Fisher精确检验"
    } else {
      valid_test <- chisq.test(valid_table)
      test_name <- "卡方检验"
    }
    cat("\n变量:", var, "（验证集 vs 原始数据）\n")
    print(valid_table)
    cat("比例 (%):\n")
    print(round(valid_prop, 2))
    cat(test_name, "p值:", valid_test$p.value, "\n")
    valid_prop_df <- as.data.frame(valid_prop)
    colnames(valid_prop_df) <- c("group", "值", "比例")
    p <- ggplot(valid_prop_df, aes(x = group, y = 比例, fill = factor(值))) + geom_bar(stat = "identity", position = "dodge") + theme_minimal() + labs(title = paste("变量:", var, "(验证集 vs 原始数据)"), y = "比例 (%)", x = "", fill = "值") + scale_fill_brewer(palette = "Set3")
    print(p)
  }
}
cat("\n\n协变量平衡性评估结论:\n")
smd_threshold <- 0.1
high_smd_train <- smd_df$变量[smd_df$训练集_vs_原始 > smd_threshold]
high_smd_valid <- smd_df$变量[smd_df$验证集_vs_原始 > smd_threshold]
if (length(high_smd_train) > 0) {
  cat("训练集vs原始数据：以下变量的SMD值超过0.1阈值:\n")
  cat(paste(high_smd_train, collapse = ", "), "\n\n")
} else {
  cat("训练集vs原始数据：所有变量的SMD均低于0.1阈值，表明良好的协变量平衡。\n\n")
}
if (length(high_smd_valid) > 0) {
  cat("验证集vs原始数据：以下变量的SMD值超过0.1阈值:\n")
  cat(paste(high_smd_valid, collapse = ", "), "\n\n")
} else {
  cat("验证集vs原始数据：所有变量的SMD均低于0.1阈值，表明良好的协变量平衡。\n\n")
}
total_vars <- length(covariate_names)
train_percent <- round(length(high_smd_train) / total_vars * 100, 2)
valid_percent <- round(length(high_smd_valid) / total_vars * 100, 2)
cat("总体结论:\n")
if (train_percent < 10 && valid_percent < 10) {
  cat("协变量平衡性良好，分层抽样成功地保持了原始数据中的协变量分布。\n")
  cat(paste0("训练集中只有", train_percent, "%的变量、验证集中只有", valid_percent, "%的变量显示轻微不平衡，均低于通常接受的10%标准。\n"))
} else {
  cat("协变量平衡性有待改善，分层抽样可能没有完全保持原始数据中的某些协变量分布。\n")
  if (train_percent >= 10) {
    cat(paste0("训练集中有", train_percent, "%的变量显示不平衡。\n"))
  }
  if (valid_percent >= 10) {
    cat(paste0("验证集中有", valid_percent, "%的变量显示不平衡。\n"))
  }
  cat("建议考虑调整分层策略或使用倾向得分方法进行进一步平衡。\n")
}

# ========== 5. 特殊结构与生存/竞争风险分析 ==========
cat("\n【四、特殊结构与生存/竞争风险分析】\n")
cif_original <- cuminc(original_time, original_vital, cencode=0)
cif_train <- cuminc(train_time, train_vital, cencode=0)
cif_valid <- cuminc(valid_time, valid_vital, cencode=0)
# 保存累积发生率图
png(file.path(output_dir, "05_累积发生率图.png"), width = 1800, height = 600, res = 300)
par(mfrow=c(1,3))
plot(cif_original, col=c(1,2), lty=c(1,1), main="原始数据累积发生率", xlab="时间(天)", ylab="累积发生率")
legend("topleft", c("主要事件", "竞争事件"), col=c(1,2), lty=c(1,1))
plot(cif_train, col=c(1,2), lty=c(1,1), main="训练集累积发生率", xlab="时间(天)", ylab="累积发生率")
legend("topleft", c("主要事件", "竞争事件"), col=c(1,2), lty=c(1,1))
plot(cif_valid, col=c(1,2), lty=c(1,1), main="验证集累积发生率", xlab="时间(天)", ylab="累积发生率")
legend("topleft", c("主要事件", "竞争事件"), col=c(1,2), lty=c(1,1))
dev.off()
cat("累积发生率图已保存到:", file.path(output_dir, "05_累积发生率图.png"), "\n")
extract_cif_data <- function(cif_obj, event_type="1 1") {
  time_points <- cif_obj[[event_type]]$time
  cif_values <- cif_obj[[event_type]]$est
  return(list(time=time_points, cif=cif_values))
}
cif_main_original <- extract_cif_data(cif_original, "1 1")
cif_main_train <- extract_cif_data(cif_train, "1 1")
cif_main_valid <- extract_cif_data(cif_valid, "1 1")
# 保存主要事件比较图
png(file.path(output_dir, "06_主要事件比较图.png"), width = 1200, height = 800, res = 300)
par(mfrow=c(1,1))
plot(cif_main_original$time, cif_main_original$cif, type="l", col=1, main="主要事件累积发生率比较", xlab="时间(天)", ylab="累积发生率")
lines(cif_main_train$time, cif_main_train$cif, col=2, lty=2)
lines(cif_main_valid$time, cif_main_valid$cif, col=3, lty=3)
legend("topleft", c("原始数据", "训练集", "验证集"), col=c(1,2,3), lty=c(1,2,3))
dev.off()
cat("主要事件比较图已保存到:", file.path(output_dir, "06_主要事件比较图.png"), "\n")
cif_comp_original <- extract_cif_data(cif_original, "1 2")
cif_comp_train <- extract_cif_data(cif_train, "1 2")
cif_comp_valid <- extract_cif_data(cif_valid, "1 2")
# 保存竞争事件比较图
png(file.path(output_dir, "07_竞争事件比较图.png"), width = 1200, height = 800, res = 300)
plot(cif_comp_original$time, cif_comp_original$cif, type="l", col=1, main="竞争事件累积发生率比较", xlab="时间(天)", ylab="累积发生率")
lines(cif_comp_train$time, cif_comp_train$cif, col=2, lty=2)
lines(cif_comp_valid$time, cif_comp_valid$cif, col=3, lty=3)
legend("topleft", c("原始数据", "训练集", "验证集"), col=c(1,2,3), lty=c(1,2,3))
dev.off()
cat("竞争事件比较图已保存到:", file.path(output_dir, "07_竞争事件比较图.png"), "\n")
par(mfrow=c(1,3))
event_dist <- function(events) {
  table_events <- table(events)
  prop_events <- prop.table(table_events)
  result_count <- c(0, 0, 0)
  result_prop <- c(0, 0, 0)
  for(i in 0:2) {
    if(as.character(i) %in% names(table_events)) {
      idx <- which(names(table_events) == as.character(i))
      result_count[i+1] <- table_events[idx]
      result_prop[i+1] <- prop_events[idx]
    }
  }
  return(data.frame(Count = result_count, Proportion = result_prop))
}
event_dist_original <- event_dist(original_vital)
event_dist_train <- event_dist(train_vital)
event_dist_valid <- event_dist(valid_vital)
barplot(event_dist_original$Count, names.arg=c("删失", "主要事件", "竞争事件"), main="原始数据事件分布")
barplot(event_dist_train$Count, names.arg=c("删失", "主要事件", "竞争事件"), main="训练集事件分布")
barplot(event_dist_valid$Count, names.arg=c("删失", "主要事件", "竞争事件"), main="验证集事件分布")
par(mfrow=c(2,3))
event1_original <- ifelse(original_vital == 1, 1, 0)
event2_original <- ifelse(original_vital == 2, 1, 0)
fit1_original <- survfit(Surv(original_time, event1_original) ~ 1)
fit2_original <- survfit(Surv(original_time, event2_original) ~ 1)
plot(fit1_original, main="原始数据 - 主要事件", xlab="时间(天)", ylab="概率")
plot(fit2_original, main="原始数据 - 竞争事件", xlab="时间(天)", ylab="概率")
event1_train <- ifelse(train_vital == 1, 1, 0)
event2_train <- ifelse(train_vital == 2, 1, 0)
fit1_train <- survfit(Surv(train_time, event1_train) ~ 1)
fit2_train <- survfit(Surv(train_time, event2_train) ~ 1)
plot(fit1_train, main="训练集 - 主要事件", xlab="时间(天)", ylab="概率")
plot(fit2_train, main="训练集 - 竞争事件", xlab="时间(天)", ylab="概率")
event1_valid <- ifelse(valid_vital == 1, 1, 0)
event2_valid <- ifelse(valid_vital == 2, 1, 0)
fit1_valid <- survfit(Surv(valid_time, event1_valid) ~ 1)
fit2_valid <- survfit(Surv(valid_time, event2_valid) ~ 1)
plot(fit1_valid, main="验证集 - 主要事件", xlab="时间(天)", ylab="概率")
plot(fit2_valid, main="验证集 - 竞争事件", xlab="时间(天)", ylab="概率")
par(mfrow=c(1,3))
event_reversed_original <- ifelse(original_vital == 0, 1, 0)
km_original <- survfit(Surv(original_time, event_reversed_original) ~ 1)
plot(km_original, main="原始数据 - 删失分布", xlab="时间(天)", ylab="删失概率")
event_reversed_train <- ifelse(train_vital == 0, 1, 0)
km_train <- survfit(Surv(train_time, event_reversed_train) ~ 1)
plot(km_train, main="训练集 - 删失分布", xlab="时间(天)", ylab="删失概率")
event_reversed_valid <- ifelse(valid_vital == 0, 1, 0)
km_valid <- survfit(Surv(valid_time, event_reversed_valid) ~ 1)
plot(km_valid, main="验证集 - 删失分布", xlab="时间(天)", ylab="删失概率")
par(mfrow=c(1,1))
km_extract <- function(km_obj) {
  time_points <- km_obj$time
  surv_prob <- km_obj$surv
  return(list(time=time_points, surv=surv_prob))
}
km_orig_data <- km_extract(km_original)
km_train_data <- km_extract(km_train)
km_valid_data <- km_extract(km_valid)
plot(km_orig_data$time, km_orig_data$surv, type="l", col=1, main="删失分布比较", xlab="时间(天)", ylab="删失概率")
lines(km_train_data$time, km_train_data$surv, col=2, lty=2)
lines(km_valid_data$time, km_valid_data$surv, col=3, lty=3)
legend("topright", c("原始数据", "训练集", "验证集"), col=c(1,2,3), lty=c(1,2,3))
cat("\n统计检验比较：\n")
group_all <- c(rep(1, length(original_time)), rep(2, length(train_time)), rep(3, length(valid_time)))
time_all <- c(original_time, train_time, valid_time)
event_all <- c(original_vital, train_vital, valid_vital)
gray_test_result <- cuminc(time_all, event_all, group=group_all, cencode=0)
print("主要事件和竞争事件的灰检验结果:")
print(gray_test_result)
censor_event <- c(ifelse(original_vital == 0, 1, 0), ifelse(train_vital == 0, 1, 0), ifelse(valid_vital == 0, 1, 0))
censor_test <- survdiff(Surv(time_all, censor_event) ~ group_all)
print("删失分布对数秩检验结果:")
print(censor_test)

# ========== 6. 生成总结报告 ==========
cat("\n【五、生成验证报告】\n")

# 创建总结报告
report_content <- paste0(
  "OSA心血管死亡风险预测模型 - 分层后验证报告\n",
  paste(rep("=", 60), collapse=""), "\n",
  "生成时间: ", Sys.time(), "\n",
  "数据来源: ", basename(original_path), "\n\n",

  "一、数据概况\n",
  paste(rep("-", 30), collapse=""), "\n",
  "原始数据: ", length(original_vital), " 例\n",
  "训练集: ", length(train_vital), " 例 (", round(length(train_vital)/length(original_vital)*100, 1), "%)\n",
  "验证集: ", length(valid_vital), " 例 (", round(length(valid_vital)/length(original_vital)*100, 1), "%)\n\n",

  "二、事件分布验证\n",
  paste(rep("-", 30), collapse=""), "\n",
  "训练集事件分布卡方检验 p值: ", round(train_chisq$p.value, 4), "\n",
  "验证集事件分布卡方检验 p值: ", round(valid_chisq$p.value, 4), "\n",
  "分层效果: ", ifelse(train_chisq$p.value > 0.05 && valid_chisq$p.value > 0.05, "良好", "需要关注"), "\n\n",

  "三、时间分布验证\n",
  paste(rep("-", 30), collapse=""), "\n",
  "训练集KS检验 p值: ", round(ks_train$p.value, 4), "\n",
  "验证集KS检验 p值: ", round(ks_valid$p.value, 4), "\n",
  "时间分布一致性: ", ifelse(ks_train$p.value > 0.05 && ks_valid$p.value > 0.05, "良好", "需要关注"), "\n\n",

  "四、协变量平衡性\n",
  paste(rep("-", 30), collapse=""), "\n",
  "SMD > 0.1的变量数 (训练集): ", length(high_smd_train), "/", length(covariate_names), "\n",
  "SMD > 0.1的变量数 (验证集): ", length(high_smd_valid), "/", length(covariate_names), "\n",
  "协变量平衡性: ", ifelse(length(high_smd_train) < length(covariate_names)*0.1 &&
                         length(high_smd_valid) < length(covariate_names)*0.1, "良好", "需要关注"), "\n\n",

  "五、生成的可视化文件\n",
  paste(rep("-", 30), collapse=""), "\n",
  "输出目录: ", output_dir, "\n",
  "01_事件分布图.png\n",
  "02_时间分布图.png\n",
  "03_按事件类型时间分布图.png\n",
  "04_协变量SMD图.png\n",
  "05_累积发生率图.png\n",
  "06_主要事件比较图.png\n",
  "07_竞争事件比较图.png\n",
  "分层验证总结报告.txt\n\n",

  "六、总体结论\n",
  paste(rep("-", 30), collapse=""), "\n",
  ifelse(train_chisq$p.value > 0.05 && valid_chisq$p.value > 0.05 &&
         ks_train$p.value > 0.05 && ks_valid$p.value > 0.05 &&
         length(high_smd_train) < length(covariate_names)*0.1 &&
         length(high_smd_valid) < length(covariate_names)*0.1,
         "分层抽样效果良好，训练集和验证集均能很好地代表原始数据的特征分布。",
         "分层抽样存在一定问题，建议检查具体的差异项目并考虑调整抽样策略。"), "\n"
)

# 保存报告
report_file <- file.path(output_dir, "分层验证总结报告.txt")
writeLines(report_content, report_file)
cat("验证报告已保存到:", report_file, "\n")

cat("\n", paste(rep("=", 60), collapse=""), "\n")
cat("所有可视化结果和报告已保存到目录:", output_dir, "\n")
cat(paste(rep("=", 60), collapse=""), "\n")

# 列出生成的文件
cat("\n生成的文件清单:\n")
generated_files <- c(
  "01_事件分布图.png",
  "02_时间分布图.png",
  "03_按事件类型时间分布图.png",
  "04_协变量SMD图.png",
  "05_累积发生率图.png",
  "06_主要事件比较图.png",
  "07_竞争事件比较图.png",
  "分层验证总结报告.txt"
)

for (i in 1:length(generated_files)) {
  file_path <- file.path(output_dir, generated_files[i])
  if (file.exists(file_path)) {
    file_size <- round(file.info(file_path)$size / 1024, 1)
    cat(sprintf("%d. %s (%.1f KB)\n", i, generated_files[i], file_size))
  } else {
    cat(sprintf("%d. %s (文件未生成)\n", i, generated_files[i]))
  }
}

cat("\n验证完成！请查看输出目录中的文件。\n")