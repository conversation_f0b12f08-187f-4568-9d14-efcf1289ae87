# =======================================
# OSA竞争风险建模 - 分层抽样训练/验证集划分
# =======================================

# 加载必要的R包
library(readxl)     # 用于读取Excel文件
library(writexl)    # 用于写入Excel文件
library(dplyr)      # 用于数据处理
library(caret)      # 用于分层抽样

# =======================================
# 配置参数 - 请根据实际情况修改
# =======================================

# 文件路径配置
input_path <- "D:/FG/EXCEL/R-clean (2).xlsx"  # 输入文件路径
train_save_path <- "D:/FG/EXCEL/R-train.xlsx"    # 训练集保存路径
valid_save_path <- "D:/FG/EXCEL/R-valid.xlsx"    # 验证集保存路径

# 分层抽样参数
train_ratio <- 0.8        # 训练集比例 (80%)
random_seed <- 2024       # 随机种子
stratify_var <- "vital"   # 分层变量名

# 检查输入文件是否存在
if(!file.exists(input_path)) {
  stop("输入文件不存在，请检查路径: ", input_path)
}

# 1. 读取数据
cat("正在读取数据...\n")
raw_data <- read_excel(input_path)

# =======================================
# 2. 数据结构检查和变量识别
# =======================================

cat("数据维度：", dim(raw_data), "\n")
cat("列名：", paste(colnames(raw_data), collapse=", "), "\n\n")

# 智能识别关键变量
key_vars <- list()

# 识别事件变量
if(stratify_var %in% colnames(raw_data)) {
  key_vars$vital <- stratify_var
  cat("✓ 找到事件变量:", stratify_var, "\n")
} else {
  # 尝试其他可能的事件变量名
  possible_vital <- c("vital", "event", "status", "outcome")
  for(var in possible_vital) {
    if(var %in% colnames(raw_data)) {
      key_vars$vital <- var
      stratify_var <- var
      cat("✓ 自动识别事件变量:", var, "\n")
      break
    }
  }
  if(is.null(key_vars$vital)) {
    stop("未找到事件变量，请检查变量名")
  }
}

# 识别时间相关变量
time_vars <- c("time", "time_corrected", "follow_time", "survival_time")
for(var in time_vars) {
  if(var %in% colnames(raw_data)) {
    key_vars$time <- var  # 统一使用time作为键名
    cat("✓ 找到时间变量:", var, "\n")
    break  # 找到第一个就停止
  }
}

# 识别ID变量
if("ID" %in% colnames(raw_data) || "id" %in% colnames(raw_data)) {
  key_vars$id <- ifelse("ID" %in% colnames(raw_data), "ID", "id")
  cat("✓ 找到ID变量:", key_vars$id, "\n")
}

cat("\n")

# =======================================
# 3. 数据结构验证
# =======================================

cat("=== 数据结构验证 ===\n")

# 验证数据结构是否符合预期
expected_structure <- list(
  id_col = "ID",           # 第一列应该是ID
  vital_col = "vital",     # 事件编码列
  time_col = "time"        # 时间列
)

# 检查第一列是否为ID
first_col <- colnames(raw_data)[1]
if(first_col == "ID") {
  cat("✓ 第一列为ID列:", first_col, "\n")
} else {
  cat("⚠ 第一列不是ID，实际为:", first_col, "\n")
}

# 检查vital和time列是否存在
if("vital" %in% colnames(raw_data)) {
  cat("✓ 找到vital列（事件编码）\n")
} else {
  stop("错误：未找到vital列，请检查数据结构")
}

if("time" %in% colnames(raw_data)) {
  cat("✓ 找到time列（时间变量）\n")
} else {
  stop("错误：未找到time列，请检查数据结构")
}

# 识别自变量列（除了ID、vital、time之外的列）
exclude_cols <- c("ID", "vital", "time")
predictor_cols <- setdiff(colnames(raw_data), exclude_cols)
cat("✓ 识别到", length(predictor_cols), "个自变量列\n")
cat("自变量列名:", paste(predictor_cols[1:min(5, length(predictor_cols))], collapse=", "))
if(length(predictor_cols) > 5) cat("...")
cat("\n")

# 检查二分类变量
binary_vars <- c()
continuous_vars <- c()

for(col in predictor_cols) {
  if(is.numeric(raw_data[[col]])) {
    unique_vals <- unique(raw_data[[col]][!is.na(raw_data[[col]])])
    if(all(unique_vals %in% c(0, 1))) {
      binary_vars <- c(binary_vars, col)
    } else {
      continuous_vars <- c(continuous_vars, col)
    }
  }
}

cat("✓ 识别到", length(binary_vars), "个二分类变量（0,1编码）\n")
cat("✓ 识别到", length(continuous_vars), "个连续变量\n")

# =======================================
# 4. 事件分布检查和数据质量评估
# =======================================

vital_data <- raw_data[[key_vars$vital]]

cat("=== 原始数据事件分布 ===\n")
event_table <- table(vital_data)
event_prop <- prop.table(event_table)
print(event_table)
cat("\n事件比例：\n")
print(round(event_prop, 4))

cat("\n事件编码说明：\n")
cat("0 = 删失事件\n")
cat("1 = 主要事件(心血管死亡)\n") 
cat("2 = 竞争事件(其他死亡)\n")

# 检查是否有缺失值
missing_vital <- sum(is.na(vital_data))
if(missing_vital > 0) {
  warning("事件变量中有", missing_vital, "个缺失值，将被排除")
  raw_data <- raw_data[!is.na(vital_data), ]
  vital_data <- raw_data[[key_vars$vital]]
}

# 检查事件分布是否合理
n_main <- sum(vital_data == 1, na.rm = TRUE)
n_competing <- sum(vital_data == 2, na.rm = TRUE)
n_censored <- sum(vital_data == 0, na.rm = TRUE)

cat("\n详细事件统计：\n")
cat("主要事件(心血管死亡):", n_main, "例 (", round(n_main/nrow(raw_data)*100, 1), "%)\n")
cat("竞争事件(其他死亡):", n_competing, "例 (", round(n_competing/nrow(raw_data)*100, 1), "%)\n")
cat("删失事件:", n_censored, "例 (", round(n_censored/nrow(raw_data)*100, 1), "%)\n")

# =======================================
# 4. 分层抽样执行
# =======================================

cat("\n=== 执行分层抽样 ===\n")
cat("训练集比例:", train_ratio, "\n")
cat("验证集比例:", 1-train_ratio, "\n")
cat("随机种子:", random_seed, "\n")

set.seed(random_seed)
train_idx <- createDataPartition(vital_data, p = train_ratio, list = FALSE)

# 划分数据集
train_data <- raw_data[train_idx, ]
valid_data <- raw_data[-train_idx, ]

cat("训练集样本数:", nrow(train_data), "\n")
cat("验证集样本数:", nrow(valid_data), "\n")

# =======================================
# 5. 分层效果验证
# =======================================

cat("\n=== 分层效果验证 ===\n")

# 训练集事件分布
train_vital <- train_data[[key_vars$vital]]
train_table <- table(train_vital)
train_prop <- prop.table(train_table)

cat("训练集事件分布：\n")
print(train_table)
cat("训练集事件比例：\n")
print(round(train_prop, 4))

# 验证集事件分布  
valid_vital <- valid_data[[key_vars$vital]]
valid_table <- table(valid_vital)
valid_prop <- prop.table(valid_table)

cat("\n验证集事件分布：\n")
print(valid_table)
cat("验证集事件比例：\n")
print(round(valid_prop, 4))

# 比例差异检查
cat("\n=== 分层质量评估 ===\n")
original_prop <- prop.table(table(vital_data))

for(event in names(original_prop)) {
  orig_p <- original_prop[event]
  train_p <- ifelse(event %in% names(train_prop), train_prop[event], 0)
  valid_p <- ifelse(event %in% names(valid_prop), valid_prop[event], 0)
  
  train_diff <- abs(train_p - orig_p)
  valid_diff <- abs(valid_p - orig_p)
  
  cat("事件", event, ":\n")
  cat("  原始比例:", round(orig_p, 4), "\n")
  cat("  训练集比例:", round(train_p, 4), " (差异:", round(train_diff, 4), ")\n")
  cat("  验证集比例:", round(valid_p, 4), " (差异:", round(valid_diff, 4), ")\n")
}

# 卡方检验评估分层效果
if(length(unique(vital_data)) > 1) {
  # 创建列联表
  contingency_table <- rbind(
    train = as.numeric(train_table[names(original_prop)]),
    valid = as.numeric(valid_table[names(original_prop)])
  )
  colnames(contingency_table) <- names(original_prop)
  
  # 进行卡方检验
  chi_test <- chisq.test(contingency_table)
  cat("\n卡方检验结果 (H0: 训练集和验证集事件分布相同):\n")
  cat("χ² =", round(chi_test$statistic, 4), ", p =", round(chi_test$p.value, 4), "\n")
  
  if(chi_test$p.value > 0.05) {
    cat("✓ 分层效果良好 (p > 0.05)\n")
  } else {
    cat("⚠ 分层效果可能不理想 (p ≤ 0.05)\n")
  }
}

# =======================================
# 6. 保存结果和生成报告
# =======================================

cat("\n=== 保存结果 ===\n")

# 创建输出目录
train_dir <- dirname(train_save_path)
valid_dir <- dirname(valid_save_path)
if(!dir.exists(train_dir)) dir.create(train_dir, recursive = TRUE)
if(!dir.exists(valid_dir)) dir.create(valid_dir, recursive = TRUE)

# 保存数据集
write_xlsx(train_data, train_save_path)
write_xlsx(valid_data, valid_save_path)

# 生成分层报告
report_path <- gsub("\\.xlsx$", "_分层报告.txt", train_save_path)
report_content <- paste0(
  "OSA竞争风险建模 - 分层抽样报告\n",
  paste(rep("=", 50), collapse=""), "\n",
  "处理时间: ", Sys.time(), "\n",
  "输入文件: ", input_path, "\n\n",
  
  "分层参数:\n",
  "- 训练集比例: ", train_ratio, "\n",
  "- 验证集比例: ", 1-train_ratio, "\n", 
  "- 随机种子: ", random_seed, "\n",
  "- 分层变量: ", key_vars$vital, "\n\n",
  
  "数据分布:\n",
  "- 总样本数: ", nrow(raw_data), "\n",
  "- 训练集: ", nrow(train_data), " (", round(nrow(train_data)/nrow(raw_data)*100, 1), "%)\n",
  "- 验证集: ", nrow(valid_data), " (", round(nrow(valid_data)/nrow(raw_data)*100, 1), "%)\n\n",
  
  "事件分布验证:\n",
  "- 主要事件 - 原始:", sum(vital_data==1), " 训练:", sum(train_vital==1), " 验证:", sum(valid_vital==1), "\n",
  "- 竞争事件 - 原始:", sum(vital_data==2), " 训练:", sum(train_vital==2), " 验证:", sum(valid_vital==2), "\n",
  "- 删失事件 - 原始:", sum(vital_data==0), " 训练:", sum(train_vital==0), " 验证:", sum(valid_vital==0), "\n\n",
  
  "质量评估:\n",
  "- 卡方检验p值: ", ifelse(exists("chi_test"), round(chi_test$p.value, 4), "未计算"), "\n",
  "- 分层质量: ", ifelse(exists("chi_test") && chi_test$p.value > 0.05, "良好", "需检查"), "\n"
)

writeLines(report_content, report_path)

cat("数据划分完成！\n")
cat("- 训练集：", train_save_path, "\n")
cat("- 验证集：", valid_save_path, "\n") 
cat("- 分层报告：", report_path, "\n")

cat("\n=== 下一步建议 ===\n")
cat("1. 检查分层报告确认质量\n")
cat("2. 在训练集上进行异常值处理\n")
cat("3. 在训练集上进行缺失值插补\n")
cat("4. 使用相同参数处理验证集\n")
cat("5. 避免验证集信息泄露到训练过程\n")