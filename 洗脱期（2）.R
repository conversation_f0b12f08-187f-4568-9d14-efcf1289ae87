# 加载必要的包
library(readxl)      # 用于读取Excel文件
library(writexl)     # 用于写入Excel文件
library(dplyr)       # 用于数据处理

# 设置参数
washout_period <- 180  # 洗脱期为6个月（180天）
input_file <- "D:/FG/EXCEL/R-clean (1).xlsx"
output_file <- "D:/FG/EXCEL/R-clean (2).xlsx"

# 读取数据
data <- read_excel(input_file)
cat("原始数据集包含", nrow(data), "行\n")

# 确定time和vital变量的列索引
ncol_data <- ncol(data)
time_col <- ncol_data - 1  # 倒数第二列是time
vital_col <- ncol_data     # 倒数第一列是vital

# 重命名最后两列，确保它们有明确的名称
names(data)[time_col] <- "time"
names(data)[vital_col] <- "vital"

# 应用洗脱期规则
# 1. 排除早期（6个月内）发生主要事件(vital=1)或竞争事件(vital=2)的观察值
# 2. 保留早期（6个月内）发生删失事件(vital=0)的观察值
filtered_data <- data %>%
  filter(!(time <= washout_period & (vital == 1 | vital == 2)))

cat("应用洗脱期后数据集包含", nrow(filtered_data), "行\n")
cat("排除了", nrow(data) - nrow(filtered_data), "行早期死亡数据\n")

# 获取排除的记录信息以便核对
excluded <- data %>%
  filter(time <= washout_period & (vital == 1 | vital == 2))
cat("排除的早期死亡记录分布：\n")
cat("主要事件 (vital=1):", sum(excluded$vital == 1), "行\n")
cat("竞争事件 (vital=2):", sum(excluded$vital == 2), "行\n")

# 保存结果到新的Excel文件
write_xlsx(filtered_data, output_file)
cat("已将处理后的数据保存至", output_file, "\n")