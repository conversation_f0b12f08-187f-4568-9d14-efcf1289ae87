# 缺失值处理脚本 - 分层插补策略
# 实施规则：
# 1. 缺失率 > 3% 的变量 → 随机森林插补
# 2. 缺失率 ≤ 3% 的变量 → 预测均值匹配
# 3. 所有变量添加缺失指示器（0/1标记）

# 数据结构说明:
# - 第一列：ID编号
# - vital列：事件编码（0=删失事件, 1=主要事件, 2=竞争事件）
# - time列：时间变量（单位：天）
# - 其余列：自变量（连续变量 + 二分类变量(0,1)）
# 注意：vital和time是因变量，不参与缺失值插补

# 加载必要包
if (!require("pacman")) install.packages("pacman")
pacman::p_load(readxl, writexl, mice, dplyr, randomForest, purrr)

cat("========== 缺失值处理开始 [", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "] ==========\n")

# 1. 数据读取
cat("1/7 读取数据...\n")
train_path <- "D:/FG/EXCEL/R-train.xlsx"
valid_path <- "D:/FG/EXCEL/R-valid.xlsx"

train_data <- read_excel(train_path) 
valid_data <- read_excel(valid_path)
cat("    训练集: ", nrow(train_data), "行 x ", ncol(train_data), "列\n")
cat("    验证集: ", nrow(valid_data), "行 x ", ncol(valid_data), "列\n")

# 2. 识别数据结构并分离变量
cat("2/7 识别数据结构并分离变量...\n")

# 检查数据结构
if (!"vital" %in% colnames(train_data)) {
  stop("错误：未找到vital列（事件编码）")
}
if (!"time" %in% colnames(train_data)) {
  stop("错误：未找到time列（时间变量）")
}

# 分离ID、因变量和自变量
train_id <- train_data[, 1]  # 第一列ID
valid_id <- valid_data[, 1]

# 提取因变量（vital和time）
train_outcome <- train_data[, c("vital", "time")]
valid_outcome <- valid_data[, c("vital", "time")]

# 提取自变量（除ID、vital、time外的所有列）
exclude_cols <- c(colnames(train_data)[1], "vital", "time")  # ID、vital、time
train_features <- train_data[, !colnames(train_data) %in% exclude_cols]
valid_features <- valid_data[, !colnames(valid_data) %in% exclude_cols]

cat("    ID列: ", colnames(train_data)[1], "\n")
cat("    因变量: vital, time\n")
cat("    自变量数量: ", ncol(train_features), "\n")

# 验证因变量编码
vital_values <- unique(train_data$vital)
cat("    vital编码: ", paste(sort(vital_values), collapse=", "), "\n")
if (!all(vital_values %in% c(0, 1, 2))) {
  warning("vital编码不符合预期（应为0,1,2）")
}

# 3. 识别自变量类型
cat("3/7 识别自变量类型...\n")

# 识别二分类变量（值为0,1的变量）
is_binary <- function(x) {
  unique_vals <- unique(na.omit(x))
  length(unique_vals) <= 2 && all(unique_vals %in% c(0, 1))
}

binary_vars <- sapply(train_features, is_binary)
binary_names <- names(binary_vars)[binary_vars]
continuous_names <- names(binary_vars)[!binary_vars]

cat("    二分类变量(", length(binary_names), "个): ")
if (length(binary_names) > 0) {
  cat(paste(head(binary_names, 5), collapse = ", "))
  if (length(binary_names) > 5) cat("...")
} else {
  cat("无")
}
cat("\n")

cat("    连续变量(", length(continuous_names), "个): ")
if (length(continuous_names) > 0) {
  cat(paste(head(continuous_names, 5), collapse = ", "))
  if (length(continuous_names) > 5) cat("...")
} else {
  cat("无")
}
cat("\n")

# 4. 记录缺失模式（用于质量评估）
cat("4/7 记录缺失模式...\n")
# 记录原始缺失模式用于后续质量评估，但不保留在最终数据中
train_missing_pattern <- is.na(train_features)
valid_missing_pattern <- is.na(valid_features)

missing_vars_count <- colSums(train_missing_pattern)
cat("    记录", ncol(train_features), "个自变量的缺失模式\n")
cat("    有缺失值的变量:", sum(missing_vars_count > 0), "个\n")

# 5. 分析自变量缺失率
cat("5/7 分析自变量缺失率...\n")
missing_rates <- colMeans(is.na(train_features)) * 100
missing_stats <- data.frame(
  Variable = names(missing_rates),
  MissingRate = round(missing_rates, 2),
  VarType = ifelse(names(missing_rates) %in% binary_names, "二分类", "连续")
) %>% arrange(desc(MissingRate))

cat("    总体缺失率: ", round(mean(is.na(train_features)) * 100, 2), "%\n")
cat("    缺失率 > 10% 的变量:\n")
high_missing <- subset(missing_stats, MissingRate > 10)
if (nrow(high_missing) > 0) {
  print(high_missing)
} else {
  cat("    无\n")
}

# 6. 分层插补策略（仅对自变量）
high_miss <- missing_stats$Variable[missing_stats$MissingRate > 3]
low_miss <- missing_stats$Variable[missing_stats$MissingRate <= 3 &
                                   missing_stats$MissingRate > 0]

cat("\n    插补策略分配:\n")
cat("    随机森林插补 (缺失率>3%):", length(high_miss), "个变量\n")
cat("    预测均值匹配 (缺失率≤3%):", length(low_miss), "个变量\n")
cat("    无缺失值:", sum(missing_stats$MissingRate == 0), "个变量\n")

# 7. 训练集插补（仅对自变量）
cat("6/7 训练集插补...\n")

# 检查是否有需要插补的变量
vars_to_impute <- names(train_features)[colSums(is.na(train_features)) > 0]
if (length(vars_to_impute) == 0) {
  cat("    训练集无缺失值，跳过插补\n")
  train_imputed <- train_features
} else {
  cat("    需要插补的变量:", length(vars_to_impute), "个\n")

  set.seed(123)

  # 构建插补方法向量
  imp_methods <- character(length(names(train_features)))
  names(imp_methods) <- names(train_features)
  imp_methods[names(train_features) %in% high_miss] <- "rf"
  imp_methods[names(train_features) %in% low_miss] <- "pmm"
  imp_methods[is.na(imp_methods)] <- "pmm"  # 默认使用pmm

  # 执行MICE插补
  imp_train <- mice(
    train_features,
    m = 1,
    maxit = 5,
    method = imp_methods,
    printFlag = TRUE
  )

  train_imputed <- complete(imp_train)
  cat("    训练集插补完成\n")
}

# 8. 验证集插补（应用训练集规则）
cat("7/7 验证集插补...\n")
valid_imputed <- valid_features

# 检查验证集是否有缺失值
vars_to_impute_valid <- names(valid_features)[colSums(is.na(valid_features)) > 0]
if (length(vars_to_impute_valid) == 0) {
  cat("    验证集无缺失值，跳过插补\n")
} else {
  cat("    验证集需要插补的变量:", length(vars_to_impute_valid), "个\n")

  # 对每个有缺失的变量应用插补
  for (var in vars_to_impute_valid) {
    cat("  处理变量:", var, "- 缺失数:", sum(is.na(valid_features[[var]])), "\n")

    # 获取该变量在训练集的插补方法
    if (exists("imp_methods") && var %in% names(imp_methods)) {
      method_used <- imp_methods[var]
    } else {
      method_used <- "pmm"  # 默认使用pmm方法
    }

    # 防止NA值
    if (is.na(method_used) || is.null(method_used)) {
      method_used <- "pmm"
      cat("    使用默认PMM方法\n")
    }

    # 获取预测变量
    pred_vars <- imp_train$predictorMatrix[var, ] == 1
    pred_vars <- names(pred_vars)[pred_vars]

    # 标记是否成功插补
    imputation_success <- FALSE

    if (method_used == "pmm") {
      # 预测均值匹配
      imp_model <- imp_train$models[[var]]
      if (!is.null(imp_model)) {
        tryCatch({
          valid_imputed[[var]] <- imputePMM(
            model = imp_model,
            newdata = valid_imputed,  # 使用已部分插补的数据
            var = var,
            donor_pool = train_imputed[[var]]
          )
          imputation_success <- TRUE
          cat("    PMM插补成功\n")
        }, error = function(e) {
          cat("    PMM插补失败:", e$message, "\n")
        })
      } else {
        cat("    PMM模型为空\n")
      }

    } else if (method_used == "rf") {
      # 随机森林
      rf_model <- imp_train$models[[var]]
      if (!is.null(rf_model)) {
        tryCatch({
          na_idx <- is.na(valid_imputed[[var]])

          if (length(pred_vars) > 0 && sum(na_idx) > 0) {
            # 确保预测变量没有缺失值
            pred_data <- valid_imputed[na_idx, pred_vars, drop = FALSE]

            # 检查预测数据是否有缺失值
            if (any(is.na(pred_data))) {
              cat("    预测变量有缺失值，使用均值插补\n")
              valid_imputed[na_idx, var] <- mean(train_imputed[[var]], na.rm = TRUE)
            } else {
              preds <- predict(rf_model, newdata = pred_data)
              valid_imputed[na_idx, var] <- preds
              cat("    RF插补成功\n")
            }
            imputation_success <- TRUE
          }
        }, error = function(e) {
          cat("    RF插补失败:", e$message, "\n")
        })
      } else {
        cat("    RF模型为空\n")
      }
    }

    # 如果插补失败，使用后备方案
    if (!imputation_success || any(is.na(valid_imputed[[var]]))) {
      na_idx <- is.na(valid_imputed[[var]])
      if (sum(na_idx) > 0) {
        cat("    使用后备方案：训练集均值插补\n")
        if (var %in% binary_names) {
          # 二分类变量使用众数
          mode_val <- as.numeric(names(sort(table(train_imputed[[var]]), decreasing = TRUE))[1])
          valid_imputed[na_idx, var] <- mode_val
        } else {
          # 连续变量使用均值
          valid_imputed[na_idx, var] <- mean(train_imputed[[var]], na.rm = TRUE)
        }
      }
    }

    # 验证插补结果
    remaining_na <- sum(is.na(valid_imputed[[var]]))
    if (remaining_na > 0) {
      cat("    警告: 仍有", remaining_na, "个缺失值!\n")
    } else {
      cat("    插补完成，无缺失值\n")
    }
  }
}

# 辅助函数：PMM插补（增强版）
imputePMM <- function(model, newdata, var, donor_pool) {
  na_idx <- is.na(newdata[[var]])
  if (!any(na_idx)) return(newdata[[var]])

  # 确保有足够的非NA值用于预测
  if (sum(!na_idx) == 0 || is.null(model)) {
    # 如果没有非NA值或模型为空，使用donor_pool的均值/众数
    if (var %in% binary_names) {
      mode_val <- as.numeric(names(sort(table(donor_pool), decreasing = TRUE))[1])
      newdata[[var]][na_idx] <- mode_val
    } else {
      newdata[[var]][na_idx] <- mean(donor_pool, na.rm = TRUE)
    }
    return(newdata[[var]])
  }

  # 防止预测错误
  tryCatch({
    # 检查预测所需的变量是否有缺失值
    model_vars <- all.vars(formula(model))[-1]  # 排除因变量
    pred_data_obs <- newdata[!na_idx, model_vars, drop = FALSE]
    pred_data_na <- newdata[na_idx, model_vars, drop = FALSE]

    # 如果预测变量有缺失值，先用均值填充
    for (mv in model_vars) {
      if (any(is.na(pred_data_obs[[mv]]))) {
        pred_data_obs[[mv]][is.na(pred_data_obs[[mv]])] <- mean(pred_data_obs[[mv]], na.rm = TRUE)
      }
      if (any(is.na(pred_data_na[[mv]]))) {
        pred_data_na[[mv]][is.na(pred_data_na[[mv]])] <- mean(newdata[[mv]], na.rm = TRUE)
      }
    }

    # 获取预测值
    pred_obs <- predict(model, newdata = pred_data_obs)
    pred_na <- predict(model, newdata = pred_data_na)

    # 确保预测值不是NA
    if (any(is.na(pred_obs)) || any(is.na(pred_na))) {
      stop("预测值包含NA")
    }

    # 寻找最近邻
    donations <- sapply(pred_na, function(x) {
      distances <- abs(pred_obs - x)
      if (all(is.na(distances)) || length(distances) == 0) {
        return(mean(donor_pool, na.rm = TRUE))
      }
      donor_pool[which.min(distances)]
    })

    newdata[[var]][na_idx] <- donations

  }, error = function(e) {
    # 预测失败时使用均值/众数代替
    cat("    PMM预测失败，使用后备方案:", e$message, "\n")
    if (var %in% binary_names) {
      mode_val <- as.numeric(names(sort(table(donor_pool), decreasing = TRUE))[1])
      newdata[[var]][na_idx] <- mode_val
    } else {
      newdata[[var]][na_idx] <- mean(donor_pool, na.rm = TRUE)
    }
  })

  return(newdata[[var]])
}

# 10. 数值精度控制
cat("应用数值精度规则...\n")
format_numeric <- function(data) {
  for (col in names(data)) {
    if (is.numeric(data[[col]])) {
      if (col == "BMI") {
        data[[col]] <- round(data[[col]], 1)
      } else if (col %in% c("NHR", "FB")) {
        data[[col]] <- round(data[[col]], 2)
      } else if (col %in% binary_names) {
        data[[col]] <- round(pmax(0, pmin(1, data[[col]])))  # 二分类变量限定在[0,1]
      } else {
        data[[col]] <- round(data[[col]], 0)  # 其他连续变量取整
      }
    }
  }
  return(data)
}

train_imputed <- format_numeric(train_imputed)
valid_imputed <- format_numeric(valid_imputed)

# 9. 合并最终数据集（规范格式）
cat("合并最终数据集...\n")

# 规范的列顺序：ID + 自变量 + 因变量(vital, time)
# 不保留缺失指示器
train_final <- bind_cols(
  train_id,                    # ID列
  train_imputed,               # 插补后的自变量
  train_outcome                # 因变量(vital, time)
)

valid_final <- bind_cols(
  valid_id,                    # ID列
  valid_imputed,               # 插补后的自变量
  valid_outcome                # 因变量(vital, time)
)

cat("    训练集最终维度: ", nrow(train_final), "行 x ", ncol(train_final), "列\n")
cat("    验证集最终维度: ", nrow(valid_final), "行 x ", ncol(valid_final), "列\n")
cat("    列结构: ID(1) + 自变量(", ncol(train_imputed), ") + 因变量(2)\n")

# 10. 保存处理结果
cat("保存处理结果...\n")
output_train <- "D:/FG/EXCEL/R-train(1).xlsx"
output_valid <- "D:/FG/EXCEL/R-valid(1).xlsx"

write_xlsx(train_final, output_train)
write_xlsx(valid_final, output_valid)

cat("    训练集已保存: ", output_train, "\n")
cat("    验证集已保存: ", output_valid, "\n")

# 13. 最终缺失值检查和处理
cat("\n===== 最终缺失值检查 =====\n")

# 检查训练集
train_final_na <- sum(is.na(train_imputed))
if (train_final_na > 0) {
  cat("警告: 训练集仍有", train_final_na, "个缺失值\n")
  # 强制处理剩余缺失值
  for (col in names(train_imputed)) {
    if (any(is.na(train_imputed[[col]]))) {
      na_count <- sum(is.na(train_imputed[[col]]))
      cat("  处理训练集变量", col, "的", na_count, "个缺失值\n")
      if (col %in% binary_names) {
        mode_val <- as.numeric(names(sort(table(train_imputed[[col]]), decreasing = TRUE))[1])
        train_imputed[[col]][is.na(train_imputed[[col]])] <- mode_val
      } else {
        train_imputed[[col]][is.na(train_imputed[[col]])] <- mean(train_imputed[[col]], na.rm = TRUE)
      }
    }
  }
}

# 检查验证集
valid_final_na <- sum(is.na(valid_imputed))
if (valid_final_na > 0) {
  cat("警告: 验证集仍有", valid_final_na, "个缺失值\n")
  # 强制处理剩余缺失值
  for (col in names(valid_imputed)) {
    if (any(is.na(valid_imputed[[col]]))) {
      na_count <- sum(is.na(valid_imputed[[col]]))
      cat("  处理验证集变量", col, "的", na_count, "个缺失值\n")
      if (col %in% binary_names) {
        # 使用训练集的众数
        mode_val <- as.numeric(names(sort(table(train_imputed[[col]]), decreasing = TRUE))[1])
        valid_imputed[[col]][is.na(valid_imputed[[col]])] <- mode_val
      } else {
        # 使用训练集的均值
        valid_imputed[[col]][is.na(valid_imputed[[col]])] <- mean(train_imputed[[col]], na.rm = TRUE)
      }
    }
  }
}

# 14. 质量评估
cat("\n===== 插补质量报告 =====\n")
cat("训练集原始缺失率: ", round(mean(is.na(train_features)) * 100, 2), "%\n")
cat("训练集插补后缺失: ", sum(is.na(train_imputed)), "\n")
cat("验证集原始缺失率: ", round(mean(is.na(valid_features)) * 100, 2), "%\n")
cat("验证集插补后缺失: ", sum(is.na(valid_imputed)), "\n")

# 高缺失变量统计比较
if (length(high_miss) > 0) {
  cat("\n高缺失变量统计对比:\n")
  for (v in head(high_miss, 3)) {
    orig <- train_features[[v]]
    imp <- train_imputed[[v]]
    
    cat(sprintf("\n变量 [%s]:", v))
    cat("\n  原始: 均值=", round(mean(orig, na.rm=T)), " SD=", round(sd(orig, na.rm=T)), 
        " 缺失=", sum(is.na(orig)))
    cat("\n  插补: 均值=", round(mean(imp)), " SD=", round(sd(imp)), 
        " 范围=[", round(min(imp)), ",", round(max(imp)), "]")
  }
}

# 12. 处理总结
cat("\n\n===== 缺失值处理总结 =====\n")
cat("处理时间: ", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n")
cat("最终数据结构: ID + ", ncol(train_imputed), "个自变量 + 2个因变量(vital, time)\n")
cat("训练集维度: ", nrow(train_final), "行 x ", ncol(train_final), "列\n")
cat("验证集维度: ", nrow(valid_final), "行 x ", ncol(valid_final), "列\n")

if (exists("high_miss") && length(high_miss) > 0) {
  cat("高缺失变量(", length(high_miss), "个): ",
      paste(head(high_miss, 3), collapse = ", "))
  if (length(high_miss) > 3) cat("...")
  cat("\n")
} else {
  cat("高缺失变量: 无\n")
}

cat("变量类型分布:\n")
cat("  - 二分类变量: ", length(binary_names), "个\n")
cat("  - 连续变量: ", length(continuous_names), "个\n")
cat("  - 因变量: 2个 (vital, time)\n")
cat("输出格式: 规范格式（ID + 自变量 + 因变量）\n")
cat("输出文件:\n")
cat("  - ", output_train, "\n")
cat("  - ", output_valid, "\n")
cat("========== 缺失值处理完成 [", format(Sys.time(), "%H:%M:%S"), "] ==========\n")