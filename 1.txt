> source("~/augment-projects/YZS/分层法（4）.R")
正在读取数据...
数据维度： 2953 30 
列名： ID, Age, Sex, BMI, NHR, WC, Pack-years, AI, AHI, HB, Minspo2, Meanspo2, T90, ODI, ESS, CHO, MSBP, MDBP, CABG, CAG, HTN, DM, AP, MI, HF, COPD, STR, FEV₁/FVC, time, vital 

✓ 找到事件变量: vital 
✓ 找到时间变量: time 
✓ 找到ID变量: ID 

=== 数据结构验证 ===
✓ 第一列为ID列: ID 
✓ 找到vital列（事件编码）
✓ 找到time列（时间变量）
✓ 识别到 27 个自变量列
自变量列名: Age, Sex, BMI, NHR, WC...
✓ 识别到 10 个二分类变量（0,1编码）
✓ 识别到 17 个连续变量
=== 原始数据事件分布 ===
vital_data
   0    1    2 
2194  212  547 

事件比例：
vital_data
     0      1      2 
0.7430 0.0718 0.1852 

事件编码说明：
0 = 删失事件
1 = 主要事件(心血管死亡)
2 = 竞争事件(其他死亡)

详细事件统计：
主要事件(心血管死亡): 212 例 ( 7.2 %)
竞争事件(其他死亡): 547 例 ( 18.5 %)
删失事件: 2194 例 ( 74.3 %)

=== 执行分层抽样 ===
训练集比例: 0.8 
验证集比例: 0.2 
随机种子: 2024 
训练集样本数: 2363 
验证集样本数: 590 

=== 分层效果验证 ===
训练集事件分布：
train_vital
   0    1    2 
1766  159  438 
训练集事件比例：
train_vital
     0      1      2 
0.7474 0.0673 0.1854 

验证集事件分布：
valid_vital
  0   1   2 
428  53 109 
验证集事件比例：
valid_vital
     0      1      2 
0.7254 0.0898 0.1847 

=== 分层质量评估 ===
事件 0 :
  原始比例: 0.743 
  训练集比例: 0.7474  (差异: 0.0044 )
  验证集比例: 0.7254  (差异: 0.0175 )
事件 1 :
  原始比例: 0.0718 
  训练集比例: 0.0673  (差异: 0.0045 )
  验证集比例: 0.0898  (差异: 0.018 )
事件 2 :
  原始比例: 0.1852 
  训练集比例: 0.1854  (差异: 1e-04 )
  验证集比例: 0.1847  (差异: 5e-04 )

卡方检验结果 (H0: 训练集和验证集事件分布相同):
χ² = 3.6486 , p = 0.1613 
✓ 分层效果良好 (p > 0.05)

=== 保存结果 ===
数据划分完成！
- 训练集： D:/FG/EXCEL/R-train.xlsx 
- 验证集： D:/FG/EXCEL/R-valid.xlsx 
- 分层报告： D:/FG/EXCEL/R-train_分层报告.txt 

=== 下一步建议 ===
1. 检查分层报告确认质量
2. 在训练集上进行异常值处理
3. 在训练集上进行缺失值插补
4. 使用相同参数处理验证集
5. 避免验证集信息泄露到训练过程
> source("~/augment-projects/YZS/分层后验证法（4-A）.R")
输出目录已存在: D:/FG/分层后验证 

【一、事件分布验证】
[1] "各数据集事件分布情况:"
    数据集 事件类型 频数 百分比 事件类型标签
1 原始数据        0 2194  74.30         删失
2 原始数据        1  212   7.18     主要事件
3 原始数据        2  547  18.52     竞争事件
4   训练集        0 1766  74.74         删失
5   训练集        1  159   6.73     主要事件
6   训练集        2  438  18.54     竞争事件
7   验证集        0  428  72.54         删失
8   验证集        1   53   8.98     主要事件
9   验证集        2  109  18.47     竞争事件

训练集与原始数据事件分布卡方检验:

	Chi-squared test for given probabilities

data:  table(train_vital)
X-squared = 0.72898, df = 2, p-value = 0.6946


验证集与原始数据事件分布卡方检验:

	Chi-squared test for given probabilities

data:  table(valid_vital)
X-squared = 2.9196, df = 2, p-value = 0.2323

事件分布图已保存到: D:/FG/分层后验证/01_事件分布图.png 

各数据集样本量情况:
    数据集 样本量   比例
1 原始数据   2953 100.00
2   训练集   2363  80.02
3   验证集    590  19.98

数据分层拆分评估结论:
训练集和验证集的事件分布与原始数据无显著差异 (p值均 > 0.05)，
说明分层抽样成功保持了原始数据中各类事件的分布比例。

各类事件的训练集vs验证集比例:
  事件类型 训练集数量 验证集数量 总数 训练集比例 验证集比例
1     删失       1766        428 2194      80.49      19.51
2 主要事件        159         53  212      75.00      25.00
3 竞争事件        438        109  547      80.07      19.93

【二、时间分布验证】
时间变量基本描述性统计:
           统计量 原始数据  训练集  验证集
mean         均值  3956.31 3952.88 3970.07
median     中位数  4268.00 4269.00 4266.00
sd         标准差  1144.19 1144.90 1142.25
min        最小值     5.00    5.00  253.00
max        最大值  5794.00 5794.00 5601.00
q25.25% 25%分位数  3685.00 3680.50 3693.75
q75.75% 75%分位数  4608.00 4594.00 4644.75

训练集与原始数据时间分布的KS检验:

	Asymptotic two-sample Kolmogorov-Smirnov test

data:  train_time and original_time
D = 0.006672, p-value = 1
alternative hypothesis: two-sided


验证集与原始数据时间分布的KS检验:

	Asymptotic two-sample Kolmogorov-Smirnov test

data:  valid_time and original_time
D = 0.026722, p-value = 0.874
alternative hypothesis: two-sided

时间分布图已保存到: D:/FG/分层后验证/02_时间分布图.png 
`summarise()` has grouped output by '数据集'. You can override using the `.groups` argument.

按事件类型分组的时间描述性统计:
# A tibble: 9 × 8
# Groups:   数据集 [3]
  数据集   事件类型 样本量  均值 中位数 标准差 最小值 最大值
  <chr>    <fct>     <int> <dbl>  <dbl>  <dbl>  <dbl>  <dbl>
1 原始数据 删失       2194 4430.  4430.   645.      5   5794
2 原始数据 主要事件    212 2565.  2682.  1167.    256   4758
3 原始数据 竞争事件    547 2595.  2653   1172.    182   5225
4 训练集   删失       1766 4418.  4423    662.      5   5794
5 训练集   主要事件    159 2543.  2771   1148.    256   4491
6 训练集   竞争事件    438 2588.  2641   1172.    182   5113
7 验证集   删失        428 4479.  4446.   567.    253   5601
8 验证集   主要事件     53 2629.  2644   1231.    370   4758
9 验证集   竞争事件    109 2625.  2747   1176.    292   5225
按事件类型时间分布图已保存到: D:/FG/分层后验证/03_按事件类型时间分布图.png 

方差分析检验三个数据集的时间分布差异:
              Df    Sum Sq Mean Sq F value Pr(>F)
数据集         2 1.395e+05   69745   0.053  0.948
Residuals   5903 7.729e+09 1309380               

Levene方差齐性检验:
Levene's Test for Homogeneity of Variance (center = median)
        Df F value Pr(>F)
group    2  0.0328 0.9677
      5903               

时间分布评估结论:
1. Kolmogorov-Smirnov检验表明，训练集和验证集的时间分布与原始数据无显著差异（p值均 > 0.05）。
2. 方差分析结果表明，三个数据集的时间平均值无显著差异（p值 > 0.05）。
3. Levene检验表明，三个数据集的时间方差无显著差异（p值 > 0.05）。

总结：各项统计检验均表明，训练集和验证集的时间分布与原始数据保持一致，
说明按照事件类型进行的分层抽样同时也良好地保持了时间数据的分布特性。

【三、协变量分布验证】
二分类变量: Sex, CABG, CAG, HTN, DM, AP, MI, HF, COPD, STR 

连续变量: Age, BMI, NHR, WC, Pack-years, AI, AHI, HB, Minspo2, Meanspo2, T90, ODI, ESS, CHO, MSBP, MDBP, FEV₁/FVC 

协变量SMD图已保存到: D:/FG/分层后验证/04_协变量SMD图.png 

训练集与原始数据的协变量平衡:
                        Stratified by group
                         训练集         原始数据       p      test SMD   
  n                        2363           2953                           
  Age (mean (SD))         65.27 (10.76)  65.21 (10.75)  0.844       0.005
  Sex = 1 (%)              1403 (59.4)    1751 (59.3)   0.976       0.002
  BMI (mean (SD))         29.48 (5.35)   29.47 (5.32)   0.941       0.002
  NHR (mean (SD))          0.23 (0.02)    0.23 (0.02)   0.776       0.008
  WC (mean (SD))         100.89 (13.25) 101.01 (13.16)  0.750       0.009
  Pack-years (mean (SD))  15.05 (23.31)  14.97 (23.00)  0.902       0.003
  AI (mean (SD))          22.62 (11.94)  22.57 (11.95)  0.877       0.004
  AHI (mean (SD))         18.18 (15.48)  18.01 (15.35)  0.697       0.011
  HB (mean (SD))          68.95 (47.76)  68.58 (46.39)  0.776       0.008
  Minspo2 (mean (SD))     82.45 (6.78)   82.50 (6.65)   0.803       0.007
  Meanspo2 (mean (SD))    93.87 (2.17)   93.89 (2.13)   0.726       0.010
  T90 (mean (SD))          5.95 (12.95)   5.72 (12.58)  0.522       0.018
  ODI (mean (SD))         78.08 (75.85)  77.76 (76.45)  0.881       0.004
  ESS (mean (SD))          8.21 (4.53)    8.21 (4.49)   0.978       0.001
  CHO (mean (SD))        208.51 (38.51) 208.32 (38.34)  0.863       0.005
  MSBP (mean (SD))       130.03 (18.97) 129.75 (19.17)  0.606       0.014
  MDBP (mean (SD))        74.43 (11.99)  74.30 (12.12)  0.711       0.010
  CABG = 1 (%)               98 ( 4.2)     137 ( 4.7)   0.429       0.024
  CAG = 1 (%)               100 ( 4.3)     121 ( 4.1)   0.852       0.007
  HTN = 1 (%)              1166 (49.3)    1446 (49.0)   0.806       0.008
  DM = 1 (%)                211 ( 9.3)     258 ( 9.1)   0.844       0.007
  AP = 1 (%)                210 ( 9.1)     267 ( 9.3)   0.875       0.006
  MI = 1 (%)                182 ( 7.9)     222 ( 7.7)   0.851       0.007
  HF = 1 (%)                 51 ( 2.3)      68 ( 2.4)   0.791       0.010
  COPD = 1 (%)               26 ( 1.1)      29 ( 1.0)   0.775       0.012
  STR = 1 (%)                94 ( 4.1)     122 ( 4.2)   0.833       0.008
  FEV₁/FVC (mean (SD))     0.76 (0.08)    0.76 (0.08)   0.481       0.020

验证集与原始数据的协变量平衡:
                        Stratified by group
                         验证集         原始数据       p      test SMD   
  n                         590           2953                           
  Age (mean (SD))         64.98 (10.71)  65.21 (10.75)  0.628       0.022
  Sex = 1 (%)               348 (59.0)    1751 (59.3)   0.924       0.006
  BMI (mean (SD))         29.42 (5.18)   29.47 (5.32)   0.855       0.008
  NHR (mean (SD))          0.23 (0.02)    0.23 (0.02)   0.483       0.032
  WC (mean (SD))         101.49 (12.82) 101.01 (13.16)  0.429       0.037
  Pack-years (mean (SD))  14.65 (21.75)  14.97 (23.00)  0.761       0.014
  AI (mean (SD))          22.36 (12.01)  22.57 (11.95)  0.704       0.017
  AHI (mean (SD))         17.35 (14.79)  18.01 (15.35)  0.334       0.044
  HB (mean (SD))          67.10 (40.45)  68.58 (46.39)  0.470       0.034
  Minspo2 (mean (SD))     82.68 (6.07)   82.50 (6.65)   0.531       0.029
  Meanspo2 (mean (SD))    93.97 (1.95)   93.89 (2.13)   0.379       0.041
  T90 (mean (SD))          4.82 (10.93)   5.72 (12.58)  0.105       0.077
  ODI (mean (SD))         76.50 (78.88)  77.76 (76.45)  0.715       0.016
  ESS (mean (SD))          8.19 (4.35)    8.21 (4.49)   0.946       0.003
  CHO (mean (SD))        207.57 (37.70) 208.32 (38.34)  0.672       0.020
  MSBP (mean (SD))       128.66 (19.90) 129.75 (19.17)  0.214       0.056
  MDBP (mean (SD))        73.81 (12.62)  74.30 (12.12)  0.372       0.040
  CABG = 1 (%)               39 ( 6.6)     137 ( 4.7)   0.060       0.085
  CAG = 1 (%)                21 ( 3.6)     121 ( 4.1)   0.602       0.029
  HTN = 1 (%)               280 (47.5)    1446 (49.0)   0.532       0.030
  DM = 1 (%)                 47 ( 8.3)     258 ( 9.1)   0.598       0.028
  AP = 1 (%)                 57 (10.0)     267 ( 9.3)   0.671       0.023
  MI = 1 (%)                 40 ( 7.0)     222 ( 7.7)   0.609       0.028
  HF = 1 (%)                 17 ( 3.0)      68 ( 2.4)   0.482       0.038
  COPD = 1 (%)                3 ( 0.5)      29 ( 1.0)   0.384       0.056
  STR = 1 (%)                28 ( 4.9)     122 ( 4.2)   0.574       0.030
  FEV₁/FVC (mean (SD))     0.76 (0.08)    0.76 (0.08)   0.086       0.079


连续型协变量的详细分析:

变量: Age （训练集 vs 原始数据）
# A tibble: 2 × 5
  group     均值 标准差 中位数   IQR
  <chr>    <dbl>  <dbl>  <dbl> <dbl>
1 原始数据  65.2   10.7     66    16
2 训练集    65.3   10.8     66    16
t检验 p值: 0.8436897 

变量: Age （验证集 vs 原始数据）
# A tibble: 2 × 5
  group     均值 标准差 中位数   IQR
  <chr>    <dbl>  <dbl>  <dbl> <dbl>
1 原始数据  65.2   10.7     66    16
2 验证集    65.0   10.7     65    16
t检验 p值: 0.627776 

变量: BMI （训练集 vs 原始数据）
# A tibble: 2 × 5
  group     均值 标准差 中位数   IQR
  <chr>    <dbl>  <dbl>  <dbl> <dbl>
1 原始数据  29.5   5.32   28.8   6.5
2 训练集    29.5   5.35   28.8   6.5
t检验 p值: 0.9410068 

变量: BMI （验证集 vs 原始数据）
# A tibble: 2 × 5
  group     均值 标准差 中位数   IQR
  <chr>    <dbl>  <dbl>  <dbl> <dbl>
1 原始数据  29.5   5.32   28.8   6.5
2 验证集    29.4   5.18   28.7   6.5
t检验 p值: 0.8524824 

变量: NHR （训练集 vs 原始数据）
# A tibble: 2 × 5
  group     均值 标准差 中位数   IQR
  <chr>    <dbl>  <dbl>  <dbl> <dbl>
1 原始数据 0.233 0.0208   0.23  0.03
2 训练集   0.233 0.0208   0.23  0.03
t检验 p值: 0.7759642 

变量: NHR （验证集 vs 原始数据）
# A tibble: 2 × 5
  group     均值 标准差 中位数   IQR
  <chr>    <dbl>  <dbl>  <dbl> <dbl>
1 原始数据 0.233 0.0208   0.23  0.03
2 验证集   0.232 0.0204   0.23  0.03
t检验 p值: 0.4784819 

变量: WC （训练集 vs 原始数据）
# A tibble: 2 × 5
  group     均值 标准差 中位数   IQR
  <chr>    <dbl>  <dbl>  <dbl> <dbl>
1 原始数据  101.   13.2    100    16
2 训练集    101.   13.2    100    17
t检验 p值: 0.7497215 

变量: WC （验证集 vs 原始数据）
# A tibble: 2 × 5
  group     均值 标准差 中位数   IQR
  <chr>    <dbl>  <dbl>  <dbl> <dbl>
1 原始数据  101.   13.2    100    16
2 验证集    101.   12.8    100    17
t检验 p值: 0.4215338 
错误于eval(predvars, data, env): 找不到对象'Pack'
此外: 警告信息:
1: In ks.test.default(train_time, original_time) : 并列的时候P-值将近似
2: In ks.test.default(valid_time, original_time) : 并列的时候P-值将近似
3: Removed 31 rows containing non-finite outside the scale range (`stat_boxplot()`). 
4: Removed 20 rows containing non-finite outside the scale range (`stat_boxplot()`). 
5: Removed 61 rows containing non-finite outside the scale range (`stat_boxplot()`). 
6: Removed 41 rows containing non-finite outside the scale range (`stat_boxplot()`). 
7: Removed 295 rows containing non-finite outside the scale range (`stat_boxplot()`). 
8: Removed 200 rows containing non-finite outside the scale range (`stat_boxplot()`). 