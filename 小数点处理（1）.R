# =============================================================================
# R脚本：处理Excel数据中的小数点位数
# 作者：数据分析师
# 日期：2025-07-20
# 功能：读取Excel文件，对不同变量设置不同的小数位数，并保存处理后的数据
# =============================================================================

# 清理工作环境
rm(list = ls())

# 加载必要的R包
# readxl: 用于读取Excel文件
# openxlsx: 用于写入Excel文件（更现代化的包）
# dplyr: 用于数据处理
# tidyr: 用于数据重塑
library(readxl)
library(openxlsx)
library(dplyr)
library(tidyr)

# 如果包未安装，请先安装：
# install.packages(c("readxl", "openxlsx", "dplyr", "tidyr"))

# =============================================================================
# 1. 数据读取
# =============================================================================

# 定义文件路径
input_file <- "D:/FG/EXCEL/R.xlsx"
output_file <- "D:/FG/EXCEL/R-clean (1).xlsx"

# 检查输入文件是否存在
if (!file.exists(input_file)) {
  stop("错误：输入文件不存在，请检查文件路径：", input_file)
}

# 读取Excel文件
cat("正在读取Excel文件...\n")
data <- read_excel(input_file)

# 显示数据基本信息
cat("数据维度：", nrow(data), "行", ncol(data), "列\n")
cat("列名：", paste(colnames(data), collapse = ", "), "\n")

# 显示数据前几行
cat("\n原始数据前5行：\n")
print(head(data, 5))

# =============================================================================
# 2. 数据验证
# =============================================================================

# 检查必要的列是否存在
required_cols <- c("ID", "vital", "time")
missing_cols <- setdiff(required_cols, colnames(data))
if (length(missing_cols) > 0) {
  stop("错误：缺少必要的列：", paste(missing_cols, collapse = ", "))
}

# 检查vital列的编码是否正确（应该包含0, 1, 2）
vital_values <- unique(data$vital)
cat("vital列的唯一值：", paste(vital_values, collapse = ", "), "\n")

# 检查time列是否为数值型
if (!is.numeric(data$time)) {
  cat("警告：time列不是数值型，尝试转换...\n")
  data$time <- as.numeric(data$time)
}

# =============================================================================
# 3. 小数点位数处理
# =============================================================================

cat("\n开始处理小数点位数...\n")

# 创建数据副本用于处理
data_processed <- data

# 处理BMI：保留1位小数
if ("BMI" %in% colnames(data_processed)) {
  data_processed$BMI <- round(data_processed$BMI, 1)
  cat("BMI列已处理为1位小数\n")
} else {
  cat("警告：未找到BMI列\n")
}

# 处理NHR：保留2位小数
if ("NHR" %in% colnames(data_processed)) {
  data_processed$NHR <- round(data_processed$NHR, 2)
  cat("NHR列已处理为2位小数\n")
} else {
  cat("警告：未找到NHR列\n")
}

# 处理FEV₁/FVC：保留2位小数
if ("FEV₁/FVC" %in% colnames(data_processed)) {
  data_processed$`FEV₁/FVC` <- round(data_processed$`FEV₁/FVC`, 2)
  cat("FEV₁/FVC列已处理为2位小数\n")
} else {
  cat("警告：未找到FEV₁/FVC列\n")
}

# 处理其余自变量：保留0位小数
# 排除ID、vital、time、BMI、NHR、FEV₁/FVC列
special_cols <- c("ID", "vital", "time", "BMI", "NHR", "FEV₁/FVC")
other_cols <- setdiff(colnames(data_processed), special_cols)

# 对其余列进行处理
for (col in other_cols) {
  # 检查是否为数值型列
  if (is.numeric(data_processed[[col]])) {
    # 检查是否为二分类变量（只包含0和1）
    unique_vals <- unique(data_processed[[col]][!is.na(data_processed[[col]])])
    
    if (all(unique_vals %in% c(0, 1))) {
      # 二分类变量，确保为整数
      data_processed[[col]] <- as.integer(data_processed[[col]])
      cat(col, "列识别为二分类变量，已转换为整数\n")
    } else {
      # 连续变量，保留0位小数
      data_processed[[col]] <- round(data_processed[[col]], 0)
      cat(col, "列已处理为0位小数\n")
    }
  } else {
    cat(col, "列不是数值型，跳过处理\n")
  }
}

# =============================================================================
# 4. 数据质量检查
# =============================================================================

cat("\n进行数据质量检查...\n")

# 检查缺失值
missing_summary <- data_processed %>%
  summarise_all(~sum(is.na(.))) %>%
  pivot_longer(cols = everything(), names_to = "变量", values_to = "缺失值数量") %>%
  filter(缺失值数量 > 0)

if (nrow(missing_summary) > 0) {
  cat("发现缺失值：\n")
  print(missing_summary)
} else {
  cat("未发现缺失值\n")
}

# 显示处理后的数据摘要
cat("\n处理后的数据摘要：\n")
print(summary(data_processed))

# =============================================================================
# 5. 保存处理后的数据
# =============================================================================

cat("\n正在保存处理后的数据...\n")

# 创建输出目录（如果不存在）
output_dir <- dirname(output_file)
if (!dir.exists(output_dir)) {
  dir.create(output_dir, recursive = TRUE)
  cat("已创建输出目录：", output_dir, "\n")
}

# 使用openxlsx包保存Excel文件
# 这个包提供更好的格式控制和兼容性
write.xlsx(data_processed, 
           file = output_file, 
           sheetName = "处理后数据",
           rowNames = FALSE,
           colNames = TRUE)

cat("数据已成功保存到：", output_file, "\n")

# =============================================================================
# 6. 生成处理报告
# =============================================================================

cat("\n=== 数据处理报告 ===\n")
cat("输入文件：", input_file, "\n")
cat("输出文件：", output_file, "\n")
cat("处理时间：", Sys.time(), "\n")
cat("数据行数：", nrow(data_processed), "\n")
cat("数据列数：", ncol(data_processed), "\n")

# 显示各列的数据类型和小数位数设置
cat("\n各列处理情况：\n")
for (col in colnames(data_processed)) {
  if (col == "BMI") {
    cat(sprintf("%-10s: %s (保留1位小数)\n", col, class(data_processed[[col]])[1]))
  } else if (col %in% c("NHR", "FEV₁/FVC")) {
    cat(sprintf("%-10s: %s (保留2位小数)\n", col, class(data_processed[[col]])[1]))
  } else if (col %in% c("ID", "vital", "time")) {
    cat(sprintf("%-10s: %s (特殊列，未处理)\n", col, class(data_processed[[col]])[1]))
  } else {
    cat(sprintf("%-10s: %s (保留0位小数)\n", col, class(data_processed[[col]])[1]))
  }
}

# 显示处理后数据的前几行
cat("\n处理后数据前5行：\n")
print(head(data_processed, 5))

cat("\n数据处理完成！\n")

# =============================================================================
# 脚本结束
# =============================================================================
